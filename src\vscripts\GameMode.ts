import { reloadable } from "./lib/tstl-utils";
import { modifier_panic } from "./modifiers/modifier_panic";
import { getGameStateName } from "./lib/game-utils";

const heroSelectionTime = 20;

declare global {
    interface CDOTAGameRules {
        Addon: GameMode;
    }
}


@reloadable
export class GameMode {
    public static Precache(this: void, context: CScriptPrecacheContext) {
        PrecacheResource("particle", "particles/units/heroes/hero_meepo/meepo_earthbind_projectile_fx.vpcf", context);
        PrecacheResource("soundfile", "soundevents/game_sounds_heroes/game_sounds_meepo.vsndevts", context);
    }

    public static Activate(this: void) {
        print("GameMode Activate running");
        // When the addon activates, create a new instance of this GameMode class.
        GameRules.Addon = new GameMode();
    }

    constructor() {
        print("GameMode constructor running");
        this.configure();

        // Register event listeners for dota engine events
        ListenToGameEvent("game_rules_state_change", () => this.OnStateChange(), undefined);
        ListenToGameEvent("npc_spawned", event => this.OnNpcSpawned(event), undefined);
        ListenToGameEvent("entity_killed", event => this.OnEntityKilled(event), undefined);
        
        const gameModeEntity = GameRules.GetGameModeEntity();
        print(gameModeEntity.GetClassname());
        gameModeEntity.SetExecuteOrderFilter(event => this.OnExecuteOrderFilter(event), this);

        // Register event listeners for events from the UI
        CustomGameEventManager.RegisterListener("ui_panel_closed", (_, data) => {
            print(`Player ${data.PlayerID} has closed their UI panel.`);

            // Respond by sending back an example event
            const player = PlayerResource.GetPlayer(data.PlayerID)!;
            CustomGameEventManager.Send_ServerToPlayer(player, "example_event", {
                myNumber: 42,
                myBoolean: true,
                myString: "Hello!",
                myArrayOfNumbers: [1.414, 2.718, 3.142]
            });

            // Also apply the panic modifier to the sending player's hero
            const hero = player.GetAssignedHero();
            hero.AddNewModifier(hero, undefined, modifier_panic.name, { duration: 5 });
        });
    }

    private OnExecuteOrderFilter(event: ExecuteOrderFilterEvent): boolean {
        print(event);
        return true;
    }

    private configure(): void {
        // 设置队伍最大玩家数量（增加到5人每队，避免玩家分配界面的槽位问题）
        GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.GOODGUYS, 5);
        GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.BADGUYS, 5);

        GameRules.SetShowcaseTime(20);
        GameRules.SetHeroSelectionTime(heroSelectionTime);
    }


    private OnEntityKilled(event: EntityKilledEvent): void {
        print(event);
    }

    public OnStateChange(): void {
        const state = GameRules.State_Get();
        print("OnStateChange", getGameStateName(state));

        // Add bots to lobby in tools (适配5v5配置)
        if (IsInToolsMode() && state == GameState.CUSTOM_GAME_SETUP) {
            print("Adding bots");
            // 添加 8 个机器人，为 2 个真实玩家留出空间
            for (let i = 0; i < 2; i++) {
                Tutorial.AddBot("npc_dota_hero_lina", "bad", "", false);
                Tutorial.AddBot("npc_dota_hero_lina", "good", "", false);
            }
        }

        if (state === GameState.CUSTOM_GAME_SETUP) {
            // Automatically skip setup in tools
            if (IsInToolsMode()) {
                Timers.CreateTimer(10, () => {
                    GameRules.FinishCustomGameSetup();
                });
            }
        }


        // Start game once pregame hits
        if (state === GameState.PRE_GAME) {
            Timers.CreateTimer(5, () => this.StartGame());
        }
    }

    private StartGame(): void {
  
        // Do some stuff here
    }

    

    // Called on script_reload
    public Reload() {
        print("Script reloaded!");

        // Do some stuff here
    }

    private OnNpcSpawned(event: NpcSpawnedEvent) {
        // After a hero unit spawns, apply modifier_panic for 8 seconds
        const unit = EntIndexToHScript(event.entindex) as CDOTA_BaseNPC; // Cast to npc since this is the 'npc_spawned' event
        
        if (unit.IsRealHero()) {
            // 现在通过 npc_heroes_custom.txt 配置文件来处理英雄技能覆盖
            // Lina 英雄的第一个技能已经被自动替换为 create_attack_tower
            print(`[GameMode] 英雄生成: ${unit.GetUnitName()}`);
            
            // 仍然给其他英雄添加meepo技能作为示例
            if (unit.GetUnitName() !== "npc_dota_hero_lina" && !unit.HasAbility("meepo_earthbind_ts_example")) {
                unit.AddAbility("meepo_earthbind_ts_example");
            }
        }
    }
}
