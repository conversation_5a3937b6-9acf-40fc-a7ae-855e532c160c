/**
 * 将 GameState 枚举值转换为字符串名称的函数
 * @param state 
 * @returns 
 */
export function getGameStateName(state: GameState): string {
    switch (state) {
        case GameState.INIT: return "INIT";
        case GameState.WAIT_FOR_PLAYERS_TO_LOAD: return "WAIT_FOR_PLAYERS_TO_LOAD";
        case GameState.CUSTOM_GAME_SETUP: return "CUSTOM_GAME_SETUP";
        case GameState.PLAYER_DRAFT: return "PLAYER_DRAFT";
        case GameState.HERO_SELECTION: return "HERO_SELECTION";
        case GameState.STRATEGY_TIME: return "STRATEGY_TIME";
        case GameState.TEAM_SHOWCASE: return "TEAM_SHOWCASE";
        case GameState.WAIT_FOR_MAP_TO_LOAD: return "WAIT_FOR_MAP_TO_LOAD";
        case GameState.PRE_GAME: return "PRE_GAME";
        case GameState.SCENARIO_SETUP: return "SCENARIO_SETUP";
        case GameState.GAME_IN_PROGRESS: return "GAME_IN_PROGRESS";
        case GameState.POST_GAME: return "POST_GAME";
        case GameState.DISCONNECT: return "DISCONNECT";
        default: return `UNKNOWN_STATE_${state}`;
    }
}

/**
 * 在鼠标位置创建单位
 * @param ability 
 */
export function createUnitOnMouseCursor(ability: CDOTABaseAbility): void {
    const kv  = ability.GetAbilityKeyValues() as {
        UnitName: string
    }
    const caster = ability.GetCaster();
    const point = caster.GetCursorPosition();

    // 创建攻击塔
    const tower = CreateUnitByName(
        kv.UnitName,
        point,
        false,
        caster,
        caster.GetOwner(),
        caster.GetTeamNumber()
    );

    tower.SetControllableByPlayer(caster.GetPlayerOwnerID(), true);

    // 设置塔为无敌
    tower.AddNewModifier(caster, ability as CDOTABaseAbility, "modifier_invulnerable", {});

    print(`[Create Attack Tower] 在位置 (${point.x}, ${point.y}) 创建了无敌攻击塔`);
}
