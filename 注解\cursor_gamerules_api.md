# 详细分析GameRules类及其API
_Exported on 2025/6/28 at GMT+8 21:12:16 from Cursor (1.1.6)_

---

**User**

详细分析GameRules这个类和它的API

---

**Cursor**

我需要深入分析GameRules类及其API。让我搜索项目中关于GameRules的相关信息。

让我继续搜索更多关于GameRules API的信息，包括类型定义和完整的方法列表。

根据项目代码和API文档，我来详细分析**GameRules**这个类和它的API。

## GameRules类概述

`GameRules`是Dota 2引擎中的核心全局对象，对应C++中的`CDOTAGameRules`类，负责管理游戏的规则、状态和配置。它是一个单例对象，可以在整个游戏生命周期中访问。

## 类型定义和扩展

从代码中可以看到，项目通过TypeScript声明扩展了GameRules的接口：

```7:10:src/vscripts/GameMode.ts
declare global {
    interface CDOTAGameRules {
        Addon: GameMode;
    }
}
```

这样扩展了`CDOTAGameRules`接口，添加了自定义的`Addon`属性来存储GameMode实例。

## 主要API分类

### 1. 游戏状态管理

#### `GameRules.State_Get()`
- **作用**：获取当前游戏状态
- **返回值**：`GameState`枚举值
- **使用示例**：
```60:60:src/vscripts/GameMode.ts
const state = GameRules.State_Get();
```

#### 常见游戏状态包括：
- `GameState.CUSTOM_GAME_SETUP` - 自定义游戏设置阶段
- `GameState.PRE_GAME` - 游戏开始前阶段  
- `GameState.GAME_IN_PROGRESS` - 游戏进行中
- `GameState.POST_GAME` - 游戏结束后

#### `GameRules.FinishCustomGameSetup()`
- **作用**：完成自定义游戏设置，进入下一阶段
- **使用示例**：
```73:73:src/vscripts/GameMode.ts
GameRules.FinishCustomGameSetup();
```

### 2. 队伍配置

#### `GameRules.SetCustomGameTeamMaxPlayers(team, maxPlayers)`
- **作用**：设置指定队伍的最大玩家数量
- **参数**：
  - `team`: 队伍ID (`DotaTeam.GOODGUYS` 或 `DotaTeam.BADGUYS`)
  - `maxPlayers`: 最大玩家数量
- **使用示例**：
```52:53:src/vscripts/GameMode.ts
GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.GOODGUYS, 3);
GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.BADGUYS, 3);
```

### 3. 时间管理

#### `GameRules.SetHeroSelectionTime(seconds)`
- **作用**：设置英雄选择阶段的时长
- **参数**：时间（秒）
- **使用示例**：
```56:56:src/vscripts/GameMode.ts
GameRules.SetHeroSelectionTime(heroSelectionTime);
```

#### `GameRules.SetShowcaseTime(seconds)`
- **作用**：设置展示阶段的时长
- **使用示例**：
```55:55:src/vscripts/GameMode.ts
GameRules.SetShowcaseTime(0);
```

#### `GameRules.GetGameTime()`
- **作用**：获取游戏内时间
- **来源**：从timers.lua中可以看到使用
```160:160:src/vscripts/lib/timers.lua
local now = GameRules:GetGameTime()
```

### 4. 胜负判定

#### `GameRules.SetGameWinner(team)`
- **作用**：设置游戏获胜队伍
- **参数**：队伍ID
- **来源**：API文档中提到的方法

### 5. 自定义属性存储

#### `GameRules.Addon`
- **作用**：存储自定义GameMode实例
- **用途**：实现热重载功能，保持游戏状态连续性
- **使用示例**：
```21:21:src/vscripts/GameMode.ts
GameRules.Addon = new GameMode();
```

## 计时器集成

GameRules与计时器系统紧密集成：

```287:287:src/vscripts/lib/timers.lua
GameRules.Timers = Timers
```

这使得计时器可以访问游戏时间：
```244:244:src/vscripts/lib/timers.lua
local now = GameRules:GetGameTime()
```

## 实际使用模式

### 1. 游戏配置
```typescript
private configure(): void {
    GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.GOODGUYS, 3);
    GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.BADGUYS, 3);
    GameRules.SetShowcaseTime(0);
    GameRules.SetHeroSelectionTime(heroSelectionTime);
}
```

### 2. 状态监听
```typescript
public OnStateChange(): void {
    const state = GameRules.State_Get();
    
    if (state === GameState.CUSTOM_GAME_SETUP) {
        // 游戏设置逻辑
        if (IsInToolsMode()) {
            Timers.CreateTimer(3, () => {
                GameRules.FinishCustomGameSetup();
            });
        }
    }
    
    if (state === GameState.PRE_GAME) {
        // 游戏开始前逻辑
        Timers.CreateTimer(0.2, () => this.StartGame());
    }
}
```

### 3. 热重载支持
```typescript
// 激活时设置
GameRules.Addon = new GameMode();

// 重载检查
if (GameRules.Addon !== undefined) {
    GameRules.Addon.Reload();
}
```

## 总结

GameRules是Dota 2自定义游戏开发中的核心类，提供了：

1. **游戏状态管理** - 监控和控制游戏流程
2. **队伍配置** - 设置队伍人数等参数  
3. **时间控制** - 管理各个阶段的时长
4. **全局存储** - 保存自定义数据和实例
5. **引擎接口** - 与Dota 2引擎的主要交互点

通过GameRules，开发者可以完全控制自定义游戏的规则和流程，是实现复杂游戏逻辑的基础。

```typescript
declare global {
    interface CDOTAGameRules {
        Addon: GameMode;
    }
}
```

```typescript
const state = GameRules.State_Get();
```

```typescript
GameRules.FinishCustomGameSetup();
```

```typescript
GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.GOODGUYS, 3);
GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.BADGUYS, 3);
```

```typescript
GameRules.SetHeroSelectionTime(heroSelectionTime);
```

```typescript
GameRules.SetShowcaseTime(0);
```

```lua
local now = GameRules:GetGameTime()
```

```typescript
GameRules.Addon = new GameMode();
```

```lua
GameRules.Timers = Timers
```

```lua
local now = GameRules:GetGameTime()
```

```typescript
private configure(): void {
    GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.GOODGUYS, 3);
    GameRules.SetCustomGameTeamMaxPlayers(DotaTeam.BADGUYS, 3);
    GameRules.SetShowcaseTime(0);
    GameRules.SetHeroSelectionTime(heroSelectionTime);
}
```

```typescript
public OnStateChange(): void {
    const state = GameRules.State_Get();
    
    if (state === GameState.CUSTOM_GAME_SETUP) {
        // 游戏设置逻辑
        if (IsInToolsMode()) {
            Timers.CreateTimer(3, () => {
                GameRules.FinishCustomGameSetup();
            });
        }
    }
    
    if (state === GameState.PRE_GAME) {
        // 游戏开始前逻辑
        Timers.CreateTimer(0.2, () => this.StartGame());
    }
}
```

```typescript
// 激活时设置
GameRules.Addon = new GameMode();

// 重载检查
if (GameRules.Addon !== undefined) {
    GameRules.Addon.Reload();
}
```

