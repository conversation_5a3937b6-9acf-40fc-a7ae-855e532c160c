import "./lib/timers";
import { GameMode } from "./GameMode";

// Connect GameMode.Activate and GameMode.Precache to the dota engine
/* Returns the current environment in use by the function.
 f can be a Lua function or a number that specifies the function at that stack level:
  Level 1 is the function calling getfenv. If the given function is not a Lua function, 
  or if f is 0, getfenv returns the global environment. The default for f is 1. */

print("Activating GameMode 注册");
Object.assign(getfenv(), {
    Activate: GameMode.Activate,
    Precache: GameMode.Precache,
});

if (GameRules.Addon !== undefined) {
    print("Judging Reloading GameMode");
    // This code is only run after script_reload, not at startup
    GameRules.Addon.Reload();
}
